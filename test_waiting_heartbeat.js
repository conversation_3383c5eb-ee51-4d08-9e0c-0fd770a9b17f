const axios = require('axios');

// 测试等待心跳修复
async function testWaitingHeartbeat() {
    const testData = {
        model: 'gemini-latest',
        messages: [
            {
                role: 'user',
                content: '请写一首关于春天的诗'
            }
        ],
        stream: true,
        temperature: 0.7
    };

    console.log('=== 测试等待心跳修复 ===');
    console.log('开始时间:', new Date().toISOString());
    
    const startTime = Date.now();
    let firstResponseTime = null;
    let firstDataTime = null;
    let heartbeatCount = 0;
    let dataChunkCount = 0;

    try {
        const response = await axios({
            method: 'POST',
            url: 'http://localhost:3473/gateway/v1/chat/completions',
            headers: {
                'Authorization': 'Bearer a881018',
                'Content-Type': 'application/json'
            },
            data: testData,
            responseType: 'stream',
            timeout: 60000 // 60秒超时
        });

        firstResponseTime = Date.now();
        const responseDelay = firstResponseTime - startTime;
        
        console.log(`✅ 收到响应头 (${responseDelay}ms)`);
        console.log('状态:', response.status);

        // 监控数据流
        const monitor = setInterval(() => {
            const elapsed = Date.now() - startTime;
            console.log(`[监控 ${Math.floor(elapsed/1000)}s] 心跳: ${heartbeatCount}, 数据块: ${dataChunkCount}`);
        }, 10000);

        response.data.on('data', (chunk) => {
            const now = Date.now();
            const elapsed = now - startTime;
            
            if (!firstDataTime) {
                firstDataTime = now;
                console.log(`✅ 收到第一个数据 (${elapsed}ms)`);
            }
            
            const chunkStr = chunk.toString();
            
            // 检测心跳 vs 实际数据
            if (chunkStr.includes('chatcmpl-waiting-') || (chunkStr.includes('"delta":{}') || chunkStr.includes('"content":""'))) {
                heartbeatCount++;
                console.log(`💓 心跳 ${heartbeatCount} (${elapsed}ms)`);
            } else if (chunkStr.includes('"delta":') && chunkStr.includes('"content":') && !chunkStr.includes('"content":""')) {
                dataChunkCount++;
                console.log(`📝 数据块 ${dataChunkCount} (${elapsed}ms) - ${chunk.length} bytes`);
                
                // 显示内容预览
                const lines = chunkStr.split('\n');
                for (const line of lines) {
                    if (line.startsWith('data: ') && line !== 'data: ') {
                        try {
                            const data = JSON.parse(line.substring(6));
                            if (data.choices && data.choices[0] && data.choices[0].delta && data.choices[0].delta.content) {
                                const content = data.choices[0].delta.content;
                                if (content.trim()) {
                                    console.log(`   内容: "${content}"`);
                                }
                            }
                        } catch (e) {
                            // 忽略解析错误
                        }
                    }
                }
            }
        });

        response.data.on('end', () => {
            const endTime = Date.now();
            const totalTime = endTime - startTime;
            
            clearInterval(monitor);
            
            console.log('\n=== 完成 ===');
            console.log(`总时间: ${totalTime}ms`);
            console.log(`响应延迟: ${firstResponseTime - startTime}ms`);
            console.log(`首次数据延迟: ${firstDataTime ? firstDataTime - startTime : 'N/A'}ms`);
            console.log(`总心跳数: ${heartbeatCount}`);
            console.log(`总数据块: ${dataChunkCount}`);
            
            // 验证是否解决了15秒超时问题
            if (totalTime > 15000) {
                console.log('✅ 成功处理超过15秒的请求！');
            }
        });

        response.data.on('error', (error) => {
            clearInterval(monitor);
            console.error('❌ 流错误:', error);
        });

    } catch (error) {
        const errorTime = Date.now();
        const totalTime = errorTime - startTime;
        
        console.error(`\n❌ 请求失败 (${totalTime}ms)`);
        console.error('错误:', error.message);
        console.error('状态:', error.response?.status);
        
        if (totalTime < 20000) {
            console.error('⚠️  仍然存在早期超时问题');
        }
    }
}

// 运行测试
testWaitingHeartbeat().catch(console.error);
