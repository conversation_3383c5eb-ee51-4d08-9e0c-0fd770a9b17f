const axios = require('axios');

// 模拟前端请求来调试gemini模型的524错误
async function debugGeminiRequest() {
    const testData = {
        model: 'gemini-latest',
        messages: [
            {
                role: 'user',
                content: '嗯，用户让我写一首赞美春天的诗，当前时间是2025年7月24日，星期四的中午。首先请你思考一下如何写好这首诗，然后再写出来。'
            }
        ],
        stream: true,
        temperature: 0.7,
        max_tokens: 2000
    };

    console.log('=== 开始调试 Gemini 请求 ===');
    console.log('请求时间:', new Date().toISOString());
    console.log('请求数据:', JSON.stringify(testData, null, 2));
    
    const startTime = Date.now();
    let responseStartTime = null;
    let firstDataTime = null;
    let lastDataTime = null;
    let chunkCount = 0;
    let totalBytes = 0;
    let heartbeatCount = 0;

    try {
        console.log('\n=== 发送请求到 gateway ===');
        
        const response = await axios({
            method: 'POST',
            url: 'http://localhost:3473/gateway/v1/chat/completions',
            headers: {
                'Authorization': 'Bearer a881018',
                'Content-Type': 'application/json'
            },
            data: testData,
            responseType: 'stream',
            timeout: 30000, // 30秒客户端超时，比服务端短
            validateStatus: function (status) {
                return status < 600; // 接受所有状态码用于调试
            }
        });

        responseStartTime = Date.now();
        const responseDelay = responseStartTime - startTime;
        
        console.log(`\n=== 收到响应 (${responseDelay}ms) ===`);
        console.log('响应状态:', response.status);
        console.log('响应头:', JSON.stringify(response.headers, null, 2));

        // 设置一个定时器来监控数据流
        const monitorInterval = setInterval(() => {
            const now = Date.now();
            const totalTime = now - startTime;
            const timeSinceLastData = lastDataTime ? now - lastDataTime : 0;
            
            console.log(`[监控] 总时间: ${totalTime}ms, 距离上次数据: ${timeSinceLastData}ms, 块数: ${chunkCount}, 字节: ${totalBytes}, 心跳: ${heartbeatCount}`);
            
            // 如果超过15秒没有数据，警告
            if (timeSinceLastData > 15000 && lastDataTime) {
                console.warn(`[警告] 超过15秒没有收到数据！`);
            }
        }, 5000);

        response.data.on('data', (chunk) => {
            chunkCount++;
            totalBytes += chunk.length;
            const now = Date.now();
            
            if (!firstDataTime) {
                firstDataTime = now;
                const firstDataDelay = firstDataTime - responseStartTime;
                console.log(`\n=== 收到第一个数据块 (响应后 ${firstDataDelay}ms) ===`);
            }
            
            lastDataTime = now;
            const chunkStr = chunk.toString();
            
            // 检查是否是心跳数据
            const isHeartbeat = chunkStr.includes('"delta":{}') || chunkStr.includes('"content":""');
            if (isHeartbeat) {
                heartbeatCount++;
                console.log(`[心跳 ${heartbeatCount}] 时间: ${now - startTime}ms, 大小: ${chunk.length}`);
            } else {
                console.log(`[数据 ${chunkCount}] 时间: ${now - startTime}ms, 大小: ${chunk.length}`);
                
                // 显示数据内容的前100个字符
                const preview = chunkStr.length > 100 ? chunkStr.substring(0, 100) + '...' : chunkStr;
                console.log(`[内容预览] ${preview}`);
            }
        });

        response.data.on('end', () => {
            const endTime = Date.now();
            const totalTime = endTime - startTime;
            
            clearInterval(monitorInterval);
            
            console.log(`\n=== 流结束 ===`);
            console.log(`总时间: ${totalTime}ms`);
            console.log(`总块数: ${chunkCount}`);
            console.log(`总字节: ${totalBytes}`);
            console.log(`心跳数: ${heartbeatCount}`);
            console.log(`首次数据延迟: ${firstDataTime ? firstDataTime - responseStartTime : 'N/A'}ms`);
        });

        response.data.on('error', (error) => {
            const errorTime = Date.now();
            const totalTime = errorTime - startTime;
            
            clearInterval(monitorInterval);
            
            console.error(`\n=== 流错误 (${totalTime}ms) ===`);
            console.error('错误详情:', error);
        });

        // 设置一个总体超时监控
        setTimeout(() => {
            const now = Date.now();
            const totalTime = now - startTime;
            
            if (totalTime > 25000) { // 25秒后警告
                console.warn(`\n[超时警告] 请求已运行 ${totalTime}ms，即将超时`);
            }
        }, 25000);

    } catch (error) {
        const errorTime = Date.now();
        const totalTime = errorTime - startTime;
        
        console.error(`\n=== 请求失败 (${totalTime}ms) ===`);
        console.error('错误类型:', error.constructor.name);
        console.error('错误代码:', error.code);
        console.error('错误消息:', error.message);
        
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应状态文本:', error.response.statusText);
            console.error('响应头:', error.response.headers);
            
            if (error.response.data) {
                console.error('响应数据:', error.response.data);
            }
        }
        
        console.error('完整错误对象:', error);
    }
}

// 运行调试
debugGeminiRequest().catch(console.error);
