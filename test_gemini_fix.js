const axios = require('axios');

// 测试修复后的gemini模型超时问题
async function testGeminiRequest() {
    const testData = {
        model: 'gemini-latest',
        messages: [
            {
                role: 'user',
                content: '嗯，用户让我写一首赞美春天的诗，当前时间是2025年7月24日，星期四的中午。'
            }
        ],
        stream: true,
        temperature: 0.7
    };

    try {
        console.log('Testing Gemini request with timeout fix...');
        console.log('Request data:', JSON.stringify(testData, null, 2));
        
        const response = await axios({
            method: 'POST',
            url: 'http://localhost:3473/gateway/v1/chat/completions',
            headers: {
                'Authorization': 'Bearer a881018',
                'Content-Type': 'application/json'
            },
            data: testData,
            responseType: 'stream',
            timeout: 30000 // 30秒测试超时
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);

        let responseData = '';
        let chunkCount = 0;
        
        response.data.on('data', (chunk) => {
            chunkCount++;
            const chunkStr = chunk.toString();
            responseData += chunkStr;
            console.log(`Chunk ${chunkCount}:`, chunkStr.substring(0, 100) + '...');
        });

        response.data.on('end', () => {
            console.log('Stream ended successfully');
            console.log('Total chunks received:', chunkCount);
            console.log('Response length:', responseData.length);
        });

        response.data.on('error', (error) => {
            console.error('Stream error:', error);
        });

    } catch (error) {
        console.error('Request failed:', {
            message: error.message,
            code: error.code,
            status: error.response?.status,
            statusText: error.response?.statusText
        });
    }
}

// 运行测试
testGeminiRequest();
