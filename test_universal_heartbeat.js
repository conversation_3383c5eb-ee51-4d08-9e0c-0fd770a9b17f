const axios = require('axios');

// 测试通用心跳机制
async function testModelHeartbeat(modelName, testPrompt = '请简单介绍一下人工智能') {
    const testData = {
        model: modelName,
        messages: [
            {
                role: 'user',
                content: testPrompt
            }
        ],
        stream: true,
        temperature: 0.7
    };

    console.log(`\n=== 测试模型: ${modelName} ===`);
    console.log('开始时间:', new Date().toISOString());
    
    const startTime = Date.now();
    let firstResponseTime = null;
    let firstDataTime = null;
    let waitingHeartbeatCount = 0;
    let streamHeartbeatCount = 0;
    let dataChunkCount = 0;
    let totalBytes = 0;

    try {
        const response = await axios({
            method: 'POST',
            url: 'http://localhost:3473/gateway/v1/chat/completions',
            headers: {
                'Authorization': 'Bearer a881018',
                'Content-Type': 'application/json'
            },
            data: testData,
            responseType: 'stream',
            timeout: 60000 // 60秒超时
        });

        firstResponseTime = Date.now();
        const responseDelay = firstResponseTime - startTime;
        
        console.log(`✅ 收到响应头 (${responseDelay}ms)`);
        console.log('状态:', response.status);

        // 监控数据流
        const monitor = setInterval(() => {
            const elapsed = Date.now() - startTime;
            console.log(`[监控 ${Math.floor(elapsed/1000)}s] 等待心跳: ${waitingHeartbeatCount}, 流心跳: ${streamHeartbeatCount}, 数据块: ${dataChunkCount}, 字节: ${totalBytes}`);
        }, 15000);

        response.data.on('data', (chunk) => {
            const now = Date.now();
            const elapsed = now - startTime;
            totalBytes += chunk.length;
            
            if (!firstDataTime) {
                firstDataTime = now;
                console.log(`✅ 收到第一个数据 (${elapsed}ms)`);
            }
            
            const chunkStr = chunk.toString();
            
            // 检测不同类型的心跳和数据
            if (chunkStr.includes('chatcmpl-waiting-')) {
                waitingHeartbeatCount++;
                console.log(`⏳ 等待心跳 ${waitingHeartbeatCount} (${elapsed}ms)`);
            } else if (chunkStr.includes('chatcmpl-stream-')) {
                streamHeartbeatCount++;
                console.log(`💓 流心跳 ${streamHeartbeatCount} (${elapsed}ms)`);
            } else if (chunkStr.includes('"delta":') && chunkStr.includes('"content":') && !chunkStr.includes('"content":""')) {
                dataChunkCount++;
                console.log(`📝 数据块 ${dataChunkCount} (${elapsed}ms) - ${chunk.length} bytes`);
                
                // 显示内容预览
                const lines = chunkStr.split('\n');
                for (const line of lines) {
                    if (line.startsWith('data: ') && line !== 'data: ') {
                        try {
                            const data = JSON.parse(line.substring(6));
                            if (data.choices && data.choices[0] && data.choices[0].delta && data.choices[0].delta.content) {
                                const content = data.choices[0].delta.content;
                                if (content.trim()) {
                                    console.log(`   内容: "${content.substring(0, 50)}${content.length > 50 ? '...' : ''}"`);
                                }
                            }
                        } catch (e) {
                            // 忽略解析错误
                        }
                    }
                }
            } else if (chunkStr.includes('"delta":{}') || chunkStr.includes('"content":""')) {
                // 其他类型的心跳
                console.log(`💗 通用心跳 (${elapsed}ms)`);
            }
        });

        response.data.on('end', () => {
            const endTime = Date.now();
            const totalTime = endTime - startTime;
            
            clearInterval(monitor);
            
            console.log(`\n=== ${modelName} 完成 ===`);
            console.log(`总时间: ${totalTime}ms`);
            console.log(`响应延迟: ${firstResponseTime - startTime}ms`);
            console.log(`首次数据延迟: ${firstDataTime ? firstDataTime - startTime : 'N/A'}ms`);
            console.log(`等待心跳数: ${waitingHeartbeatCount}`);
            console.log(`流心跳数: ${streamHeartbeatCount}`);
            console.log(`数据块数: ${dataChunkCount}`);
            console.log(`总字节数: ${totalBytes}`);
            
            // 验证心跳机制是否工作
            if (totalTime > 15000 && (waitingHeartbeatCount > 0 || streamHeartbeatCount > 0)) {
                console.log('✅ 心跳机制工作正常，成功处理长时间请求！');
            } else if (totalTime <= 15000) {
                console.log('✅ 请求快速完成，心跳机制待机状态正常！');
            } else {
                console.log('⚠️  长时间请求但未检测到心跳，可能存在问题');
            }
        });

        response.data.on('error', (error) => {
            clearInterval(monitor);
            console.error(`❌ ${modelName} 流错误:`, error);
        });

    } catch (error) {
        const errorTime = Date.now();
        const totalTime = errorTime - startTime;
        
        console.error(`\n❌ ${modelName} 请求失败 (${totalTime}ms)`);
        console.error('错误:', error.message);
        console.error('状态:', error.response?.status);
        
        if (totalTime < 20000) {
            console.error('⚠️  仍然存在早期超时问题');
        }
    }
}

// 测试多个模型
async function runTests() {
    console.log('=== 开始通用心跳机制测试 ===');
    
    // 测试不同类型的模型
    const modelsToTest = [
        'gemini-latest',
        'gpt-4o',
        'claude-3-5-sonnet-20241022'
    ];
    
    for (const model of modelsToTest) {
        await testModelHeartbeat(model);
        
        // 等待一段时间再测试下一个模型
        console.log('\n等待5秒后测试下一个模型...');
        await new Promise(resolve => setTimeout(resolve, 5000));
    }
    
    console.log('\n=== 所有测试完成 ===');
}

// 运行测试
runTests().catch(console.error);
